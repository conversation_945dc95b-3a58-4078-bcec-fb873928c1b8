import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';
import { dataService } from '@/lib/dataService';

interface Book {
  id: string;
  title: string;
  author: string;
  category: string;
  cover_image_url?: string;
  progress_percentage?: number;
  last_read_at?: string;
  is_favorited?: boolean;
  is_completed?: boolean;
  current_page?: number;
  total_pages?: number;
}

interface DashboardSectionProps {
  title: string;
  icon: React.ComponentType<any>;
  books: Book[];
  onBookClick: (bookId: string) => void;
  emptyMessage: string;
  emptyDescription: string;
  showProgress?: boolean;
  showLastRead?: boolean;
}

function DashboardSection({ 
  title, 
  icon: Icon, 
  books, 
  onBookClick, 
  emptyMessage, 
  emptyDescription,
  showProgress = false,
  showLastRead = false
}: DashboardSectionProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Agora mesmo';
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d atrás`;
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white rounded-2xl border border-gray-100 p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <h2 className="text-xl font-bold text-gray-900">{title}</h2>
          <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon className="w-4 h-4 text-blue-600" />
          </div>
        </div>
        <span className="text-sm text-gray-500">{books.length} livros</span>
      </div>

      {/* Books Grid */}
      <div className="space-y-4">
        {books.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Icon className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {emptyMessage}
            </h3>
            <p className="text-gray-500 text-sm mb-4">
              {emptyDescription}
            </p>
          </div>
        ) : (
          books.map((book, index) => (
            <motion.div
              key={book.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group cursor-pointer"
              onClick={() => onBookClick(book.id)}
            >
              <div className="flex items-center space-x-4 p-4 rounded-xl hover:bg-gray-50 transition-all duration-300">
                {/* Book Cover */}
                <div className="relative w-16 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center flex-shrink-0 overflow-hidden group-hover:scale-105 transition-transform duration-300">
                  {book.cover_image_url ? (
                    <img 
                      src={book.cover_image_url} 
                      alt={book.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Icons.BookOpen className="w-8 h-8 text-gray-400" />
                  )}
                  
                  {/* Play Overlay */}
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
                      <Icons.Play className="w-4 h-4 text-gray-900 ml-0.5" />
                    </div>
                  </div>
                </div>

                {/* Book Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-gray-900 text-sm truncate">
                          {book.title}
                        </h3>
                        {book.is_favorited && (
                          <Icons.Star className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mb-2">
                        {book.author}
                      </p>
                      <p className="text-xs text-gray-400">
                        {book.category}
                      </p>
                    </div>
                    
                    <div className="text-right flex-shrink-0 ml-4">
                      {showLastRead && book.last_read_at && (
                        <span className="text-xs text-gray-400 block mb-1">
                          {formatTimeAgo(book.last_read_at)}
                        </span>
                      )}
                      {showProgress && book.progress_percentage !== undefined && (
                        <span className="text-xs font-medium text-gray-600">
                          {Math.round(book.progress_percentage)}% concluído
                        </span>
                      )}
                      {book.current_page && book.total_pages && (
                        <span className="text-xs text-gray-500 block">
                          Página {book.current_page} de {book.total_pages}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  {showProgress && book.progress_percentage !== undefined && (
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <motion.div 
                          className="bg-gradient-to-r from-gray-900 to-gray-700 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${book.progress_percentage}%` }}
                          transition={{ duration: 1, delay: 0.5 + index * 0.1 }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* View All */}
      {books.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Button variant="ghost" className="w-full text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50">
            Ver todos ({books.length})
          </Button>
        </div>
      )}
    </motion.div>
  );
}

// Specific sections
export function FavoritesSection({ books, onBookClick }: { books: Book[], onBookClick: (id: string) => void }) {
  return (
    <DashboardSection
      title="Favoritos"
      icon={Icons.Star}
      books={books}
      onBookClick={onBookClick}
      emptyMessage="Nenhum favorito ainda"
      emptyDescription="Marque livros como favoritos para encontrá-los facilmente aqui."
      showLastRead={true}
    />
  );
}

export function InProgressSection({ books, onBookClick }: { books: Book[], onBookClick: (id: string) => void }) {
  return (
    <DashboardSection
      title="Em Progresso"
      icon={Icons.Clock}
      books={books}
      onBookClick={onBookClick}
      emptyMessage="Nenhuma leitura em progresso"
      emptyDescription="Comece a ler um livro para vê-lo aparecer aqui."
      showProgress={true}
      showLastRead={true}
    />
  );
}

export function CompletedSection({ books, onBookClick }: { books: Book[], onBookClick: (id: string) => void }) {
  return (
    <DashboardSection
      title="Concluídos"
      icon={Icons.CheckCircle}
      books={books}
      onBookClick={onBookClick}
      emptyMessage="Nenhum livro concluído"
      emptyDescription="Complete a leitura de um livro para vê-lo aparecer aqui."
      showLastRead={true}
    />
  );
}

export function TrendingSection({ books, onBookClick }: { books: Book[], onBookClick: (id: string) => void }) {
  return (
    <DashboardSection
      title="Tendências"
      icon={Icons.TrendingUp}
      books={books}
      onBookClick={onBookClick}
      emptyMessage="Nenhuma tendência disponível"
      emptyDescription="Os livros mais populares aparecerão aqui em breve."
      showProgress={false}
      showLastRead={false}
    />
  );
}
