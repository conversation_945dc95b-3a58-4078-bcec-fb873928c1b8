import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sidebar } from '@/components/layout/Sidebar';
import { LibraryGrid } from '@/components/library/LibraryGrid';
import { DashboardStats } from './DashboardStats';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { ReadingStreak } from './ReadingStreak';
import { PersonalizedRecommendations } from './PersonalizedRecommendations';
import { FavoritesSection, InProgressSection, CompletedSection, TrendingSection } from './DashboardSections';
import { ProfileSection } from '@/components/profile/ProfileSection';
import { PDFReader } from '@/components/reader/PDFReader';
import { supabase } from '@/lib/supabase';
import { dataService } from '@/lib/dataService';
import { BookLoader } from '@/lib/bookLoader';

interface DashboardProps {
  user: any;
  onSignOut: () => void;
}

export default function Dashboard({ user, onSignOut }: DashboardProps) {
  const [activeSection, setActiveSection] = useState('home');
  const [books, setBooks] = useState([]);
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentBook, setCurrentBook] = useState(null);
  const [showReader, setShowReader] = useState(false);

  useEffect(() => {
    loadDashboardData();
    loadBooks();
  }, []);



  const loadDashboardData = async () => {
    try {
      const dashboardData = await dataService.getDashboardData();
      setDashboardData(dashboardData);
    } catch (error) {
      console.error('Error loading dashboard:', error);
      // Fallback to basic structure on error
      setDashboardData({
        recently_read: [],
        favorites: [],
        completed: [],
        in_progress: [],
        subscription: {
          subscription_type: 'free',
          status: 'active',
          remaining_free_access: 5
        },
        stats: {
          total_summaries_read: 0,
          total_reading_time: 0,
          favorites_count: 0,
          in_progress_count: 0
        },
        streak: {
          current: 0,
          longest: 0,
          this_week: 0
        }
      });
    }
  };

  // Function to filter out problematic books (simplified after database cleanup)
  const isProblematicBook = (book: any) => {
    // Basic validation - must have valid ID and title
    if (!book.id || !book.title || typeof book.title !== 'string') {
      return true;
    }

    const title = book.title.toLowerCase().trim();
    const author = (book.author || '').toLowerCase().trim();

    const isProblematic = (
      // Basic validation failures
      title.length === 0 ||
      book.id <= 0 ||

      // Should not exist after cleanup, but just in case
      title.includes(' 3') || title.includes(' 5') || title.includes(' 6') ||
      title.length <= 3 ||
      title.startsWith('[removed]') ||
      book.category === 'REMOVED' ||

      // Very long malformed titles (should be cleaned up)
      title.length > 100 ||
      title.includes('autores') && title.includes('autores') // Double "autores"
    );



    return isProblematic;
  };

  // Function to normalize title for duplicate detection
  const normalizeTitle = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/\b(versao|completa|parafrase|os melhores discursos)\b/g, '')
      .trim();
  };

  // Function to remove duplicates, keeping the best version of each book
  const removeDuplicates = (books: any[]) => {
    const titleGroups: { [key: string]: any[] } = {};

    // Group books by normalized title
    books.forEach(book => {
      const normalizedTitle = normalizeTitle(book.title);
      if (!titleGroups[normalizedTitle]) {
        titleGroups[normalizedTitle] = [];
      }
      titleGroups[normalizedTitle].push(book);
    });

    // For each group, keep only the best book
    const uniqueBooks: any[] = [];
    Object.values(titleGroups).forEach(group => {
      if (group.length === 1) {
        uniqueBooks.push(group[0]);
      } else {
        // Sort by quality and keep the best one
        const bestBook = group.sort((a, b) => {
          let scoreA = 0, scoreB = 0;

          // Prefer proper authors
          if (a.author && a.author !== 'Autor Desconhecido') scoreA += 10;
          if (b.author && b.author !== 'Autor Desconhecido') scoreB += 10;

          // Prefer shorter, cleaner titles
          if (a.title.length < b.title.length) scoreA += 5;
          if (b.title.length < a.title.length) scoreB += 5;

          // Prefer books without parentheses
          if (!a.title.includes('(')) scoreA += 3;
          if (!b.title.includes('(')) scoreB += 3;

          // Prefer featured books
          if (a.is_featured) scoreA += 2;
          if (b.is_featured) scoreB += 2;

          return scoreB - scoreA;
        })[0];

        uniqueBooks.push(bestBook);
      }
    });

    return uniqueBooks;
  };

  const loadBooks = async () => {
    try {
      // Clear all caches aggressively
      BookLoader.clearCache();

      // Clear browser storage
      localStorage.removeItem('books_cache');
      sessionStorage.removeItem('books_cache');

      // Force fresh data with timestamp to avoid any caching
      const timestamp = Date.now();
      const { data, error } = await supabase
        .from('books')
        .select('*')
        .order('is_featured', { ascending: false });

      if (error) throw error;

      // Filter out problematic books (should be minimal after cleanup)
      const cleanBooks = (data || []).filter(book => !isProblematicBook(book));

      // Remove duplicates, keeping the best version of each book
      const uniqueBooks = removeDuplicates(cleanBooks);

      setBooks(uniqueBooks);
    } catch (error) {
      console.error('Error loading books:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReadBook = (bookId: string) => {
    // Find the book data
    const book = books.find(b => b.id.toString() === bookId.toString());
    
    if (book) {
      setCurrentBook(book);
      setShowReader(true);
    } else {
      console.error('Dashboard: Book not found for ID:', bookId);
      // Still try to open the reader with basic info
      setCurrentBook({
        id: bookId,
        title: 'Livro Selecionado',
        author: 'Autor',
        category: 'Geral'
      });
      setShowReader(true);
    }
  };

  const handleCloseReader = () => {
    setShowReader(false);
    setCurrentBook(null);
  };

  // Debounce map to prevent multiple rapid clicks
  const favoriteDebounceMap = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Force refresh function to clear all caches and reload data
  const forceRefresh = async () => {
    // Clear all caches aggressively
    BookLoader.clearCache();

    // Clear browser cache for this session
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }

    // Clear local state first
    setBooks([]);
    setDashboardData(null);

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }

    // Reload all data
    setLoading(true);
    await Promise.all([
      loadBooks(),
      loadDashboardData()
    ]);
    setLoading(false);
  };

  const handleFavoriteBook = async (bookId: string | number) => {
    const bookIdStr = String(bookId);

    // Clear existing debounce timer for this book
    const existingTimer = favoriteDebounceMap.current.get(bookIdStr);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new debounce timer
    const timer = setTimeout(async () => {
      try {
        // Check current favorite status
        const currentProgress = await dataService.getBookProgress(bookIdStr);
        const isCurrentlyFavorited = currentProgress?.is_favorited || false;
        const newFavoriteStatus = !isCurrentlyFavorited;

        // Optimistic UI update - update local state immediately
        setDashboardData(prevData => {
          if (!prevData) return prevData;

          const updateBookInList = (books: any[]) =>
            books.map(book =>
              String(book.book_id || book.id) === bookIdStr
                ? { ...book, is_favorited: newFavoriteStatus }
                : book
            );

          return {
            ...prevData,
            favorites: newFavoriteStatus
              ? [...prevData.favorites, { book_id: bookIdStr, is_favorited: true }]
              : prevData.favorites.filter(fav => String(fav.book_id) !== bookIdStr),
            recently_read: updateBookInList(prevData.recently_read),
            in_progress: updateBookInList(prevData.in_progress),
            completed: updateBookInList(prevData.completed)
          };
        });

        // Perform database update
        await dataService.toggleFavorite(bookIdStr, newFavoriteStatus);

        // Reload dashboard data to ensure consistency
        await loadDashboardData();

      } catch (error) {
        console.error('Error toggling favorite:', error);

        // Revert optimistic update on error
        await loadDashboardData();

        // Show user-friendly error message
        alert('Erro ao favoritar livro. Tente novamente.');
      } finally {
        // Clean up debounce timer
        favoriteDebounceMap.current.delete(bookIdStr);
      }
    }, 300); // 300ms debounce

    favoriteDebounceMap.current.set(bookIdStr, timer);
  };

  const handleUpdateProgress = async (bookId: string, progressPercentage: number) => {
    try {
      await dataService.updateProgress(bookId, progressPercentage);
      // Reload dashboard data to reflect changes
      await loadDashboardData();
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  // Nova função para voltar ao dashboard
  const handleLogoClick = () => {
    setActiveSection('home');
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'home':
        return (
          <div className="space-y-8">
            {/* Quick Actions */}
            <QuickActions onSectionChange={setActiveSection} />

            {/* Stats Grid */}
            {dashboardData && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <DashboardStats stats={dashboardData.stats} />
                </div>
                <div>
                  <ReadingStreak streak={dashboardData.streak} />
                </div>
              </div>
            )}

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Recent Activity */}
              <div className="lg:col-span-2">
                {dashboardData && (
                  <RecentActivity 
                    recentBooks={dashboardData.recently_read}
                    onContinueReading={handleReadBook}
                  />
                )}
              </div>

              {/* Personalized Recommendations */}
              <div>
                <PersonalizedRecommendations
                  recommendations={dashboardData?.recommendations}
                  fallbackBooks={books.filter(book => book.is_featured).slice(0, 3)}
                  onReadBook={handleReadBook}
                />
              </div>
            </div>

            {/* Featured Books */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Resumos em Destaque</h2>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={forceRefresh}
                    className="text-xs px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    disabled={loading}
                  >
                    {loading ? 'Atualizando...' : '🔄 Atualizar'}
                  </button>
                  <button
                    onClick={() => setActiveSection('library')}
                    className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Ver todos →
                  </button>
                </div>
              </div>
              <LibraryGrid
                books={books.filter(book => book.is_featured).slice(0, 8)}
                onReadBook={handleReadBook}
                onFavoriteBook={handleFavoriteBook}
                favoriteBooks={dashboardData?.favorites?.map(fav => String(fav.book_id)) || []}
                isLoading={loading}
              />
            </div>
          </div>
        );

      case 'library':
        return (
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">Biblioteca</h1>
                  <p className="text-gray-600 text-lg">
                    Explore nossa coleção completa de resumos profissionais.
                  </p>
                </div>
              </div>
            </motion.div>

            <LibraryGrid
              books={books}
              onReadBook={handleReadBook}
              onFavoriteBook={handleFavoriteBook}
              favoriteBooks={dashboardData?.favorites?.map(fav => String(fav.book_id)) || []}
              isLoading={loading}
            />
          </div>
        );

      case 'favorites':
        const favoriteBooks = dashboardData?.favorites?.map(fav => ({
          id: String(fav.book?.id || fav.book_id),
          title: fav.book?.title || 'Título não disponível',
          author: fav.book?.author || 'Autor desconhecido',
          category: fav.book?.category || 'Geral',
          duration: fav.book?.duration || 20,
          difficulty: fav.book?.difficulty || 'Intermediário',
          is_featured: fav.book?.is_featured || false,
          description: fav.book?.description || 'Descrição não disponível',
          cover_image_url: fav.book?.cover_image_url
        })) || [];

        return (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">Favoritos</h1>
              <div className="text-sm text-gray-500">
                {favoriteBooks.length} livros favoritos
              </div>
            </div>

            <FavoritesSection
              books={favoriteBooks.map(book => ({
                ...book,
                progress_percentage: dashboardData?.favorites?.find(f => String(f.book?.id || f.book_id) === book.id)?.progress_percentage,
                last_read_at: dashboardData?.favorites?.find(f => String(f.book?.id || f.book_id) === book.id)?.last_read_at,
                is_favorited: true,
                current_page: dashboardData?.favorites?.find(f => String(f.book?.id || f.book_id) === book.id)?.current_page,
                total_pages: dashboardData?.favorites?.find(f => String(f.book?.id || f.book_id) === book.id)?.total_pages
              }))}
              onBookClick={handleReadBook}
            />
          </div>
        );

      case 'progress':
        const inProgressBooks = dashboardData?.in_progress?.map(prog => ({
          id: prog.book?.id || prog.book_id,
          title: prog.book?.title || 'Título não disponível',
          author: prog.book?.author || 'Autor desconhecido',
          category: prog.book?.category || 'Geral',
          duration: prog.book?.duration || 20,
          difficulty: prog.book?.difficulty || 'Intermediário',
          is_featured: prog.book?.is_featured || false,
          description: prog.book?.description || 'Descrição não disponível',
          cover_image_url: prog.book?.cover_image_url,
          progress_percentage: prog.progress_percentage
        })) || [];

        return (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">Em Progresso</h1>
              <div className="text-sm text-gray-500">
                {inProgressBooks.length} livros em progresso
              </div>
            </div>

            <InProgressSection
              books={inProgressBooks.map(book => ({
                ...book,
                progress_percentage: dashboardData?.in_progress?.find(p => String(p.book?.id || p.book_id) === book.id)?.progress_percentage,
                last_read_at: dashboardData?.in_progress?.find(p => String(p.book?.id || p.book_id) === book.id)?.last_read_at,
                is_favorited: dashboardData?.favorites?.some(f => String(f.book?.id || f.book_id) === book.id),
                current_page: dashboardData?.in_progress?.find(p => String(p.book?.id || p.book_id) === book.id)?.current_page,
                total_pages: dashboardData?.in_progress?.find(p => String(p.book?.id || p.book_id) === book.id)?.total_pages
              }))}
              onBookClick={handleReadBook}
            />
          </div>
        );

      case 'completed':
        const completedBooks = dashboardData?.completed?.map(comp => ({
          id: String(comp.book?.id || comp.book_id || ''),
          title: comp.book?.title || 'Título não disponível',
          author: comp.book?.author || 'Autor desconhecido',
          category: comp.book?.category || 'Geral',
          duration: comp.book?.duration || 20,
          difficulty: comp.book?.difficulty || 'Intermediário',
          is_featured: comp.book?.is_featured || false,
          description: comp.book?.description || 'Descrição não disponível',
          cover_image_url: comp.book?.cover_image_url
        })) || [];

        return (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">Concluídos</h1>
              <div className="text-sm text-gray-500">
                {completedBooks.length} livros concluídos
              </div>
            </div>

            <CompletedSection
              books={completedBooks.map(book => ({
                ...book,
                progress_percentage: 100,
                last_read_at: dashboardData?.completed?.find(c => String(c.book?.id || c.book_id) === String(book.id))?.last_read_at,
                is_favorited: dashboardData?.favorites?.some(f => String(f.book?.id || f.book_id) === String(book.id)) || false,
                is_completed: true
              }))}
              onBookClick={handleReadBook}
            />
          </div>
        );

      case 'trending':
        const trendingBooks = dashboardData?.trending?.map(trend => ({
          id: String(trend.id),
          title: trend.title || 'Título não disponível',
          author: trend.author || 'Autor desconhecido',
          category: trend.category || 'Geral',
          cover_image_url: trend.cover_image_url
        })) || [];

        return (
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h1 className="text-3xl font-bold text-gray-900">Tendências</h1>
              <div className="text-sm text-gray-500">
                Livros mais populares
              </div>
            </div>

            <TrendingSection
              books={trendingBooks}
              onBookClick={handleReadBook}
            />
          </div>
        );

      case 'profile':
        return (
          <ProfileSection
            user={user}
            dashboardData={dashboardData}
          />
        );

      default:
        return (
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Seção em Desenvolvimento
            </h2>
            <p className="text-gray-600">
              Esta funcionalidade estará disponível em breve.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dashboard-container">
      {/* PDF Reader */}
      <AnimatePresence>
        {showReader && currentBook && (
          <PDFReader
            bookId={currentBook.id.toString()}
            bookTitle={currentBook.title}
            bookAuthor={currentBook.author}
            content=""
            onClose={handleCloseReader}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <Sidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        onLogoClick={handleLogoClick}
        dashboardData={dashboardData}
      />

      {/* Main Content */}
      <div className="ml-70 min-h-screen main-content">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-gray-100 px-8 py-4 sticky top-0 z-30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold text-gray-900 capitalize">
                {activeSection === 'home' ? 'Dashboard' : 
                 activeSection === 'profile' ? 'Perfil' : activeSection}
              </h2>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {user.email?.split('@')[0]}
                </div>
              </div>
              <button
                onClick={onSignOut}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors px-3 py-1 rounded-lg hover:bg-gray-100"
              >
                Sair
              </button>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="p-8">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
}