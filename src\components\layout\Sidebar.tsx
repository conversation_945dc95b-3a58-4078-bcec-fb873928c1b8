import React from 'react';
import { motion } from 'framer-motion';
import { Icons } from '@/components/ui/icons';
import { Button } from '@/components/ui/button';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  onLogoClick: () => void;
  dashboardData?: any; // Add dashboard data for dynamic counts
}

export function Sidebar({ activeSection, onSectionChange, onLogoClick, dashboardData }: SidebarProps) {
  // Get dynamic counts from dashboard data
  const getCounts = () => ({
    favorites: dashboardData?.favorites?.length || 0,
    progress: dashboardData?.in_progress?.length || 0,
    completed: dashboardData?.completed?.length || 0,
  });

  const counts = getCounts();

  const menuItems = [
    { id: 'home', label: 'Início', icon: Icons.Home },
    { id: 'library', label: 'Biblioteca', icon: Icons.BookOpen },
    { id: 'favorites', label: 'Favoritos', icon: Icons.Heart, count: counts.favorites },
    { id: 'progress', label: 'Em Progresso', icon: Icons.Clock, count: counts.progress },
    { id: 'completed', label: 'Concluídos', icon: Icons.Trophy, count: counts.completed },
    { id: 'trending', label: 'Tendências', icon: Icons.TrendingUp },
    { id: 'bookmarks', label: 'Marcadores', icon: Icons.Bookmark },
  ];

  const bottomItems = [
    { id: 'profile', label: 'Perfil', icon: Icons.User },
    { id: 'settings', label: 'Configurações', icon: Icons.Settings },
  ];

  return (
    <motion.div
      initial={{ x: -280 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className="fixed left-0 top-0 h-full w-70 bg-white border-r border-gray-100 z-40 flex flex-col"
    >
      {/* Logo - Agora clicável */}
      <div className="p-6 border-b border-gray-100">
        <motion.button 
          onClick={onLogoClick}
          className="flex items-center space-x-3 w-full text-left group"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          <div className="w-8 h-8 rounded-lg bg-gray-900 flex items-center justify-center group-hover:bg-gray-800 transition-colors">
            <Icons.BookOpen className="w-4 h-4 text-white" />
          </div>
          <span className="text-lg font-bold text-gray-900 group-hover:text-gray-700 transition-colors">
            Paretto
          </span>
        </motion.button>
      </div>

      {/* Icons.Search */}
      <div className="p-4 border-b border-gray-50">
        <div className="relative">
          <Icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar resumos..."
            className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-gray-900 focus:border-transparent transition-all"
          />
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="flex-1 p-4 space-y-1">
        {menuItems.map((item) => (
          <motion.button
            key={item.id}
            onClick={() => onSectionChange(item.id)}
            className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 ${
              activeSection === item.id
                ? 'bg-gray-900 text-white shadow-lg'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <item.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{item.label}</span>
            {item.count !== undefined && item.count > 0 && (
              <span className={`ml-auto text-xs px-2 py-1 rounded-full ${
                activeSection === item.id
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                {item.count}
              </span>
            )}
          </motion.button>
        ))}
      </nav>

      {/* Bottom Navigation */}
      <div className="p-4 border-t border-gray-100 space-y-1">
        {bottomItems.map((item) => (
          <motion.button
            key={item.id}
            onClick={() => onSectionChange(item.id)}
            className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 ${
              activeSection === item.id
                ? 'bg-gray-900 text-white'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <item.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{item.label}</span>
          </motion.button>
        ))}
      </div>
    </motion.div>
  );
}