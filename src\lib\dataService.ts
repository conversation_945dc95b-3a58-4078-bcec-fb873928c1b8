import { supabase } from './supabase'

export interface UserProgress {
  id: string
  user_id: string
  book_id: string
  progress_percentage: number
  current_page: number
  total_pages: number
  is_completed: boolean
  is_favorited: boolean
  last_read_at: string
  reading_time_minutes: number
  created_at: string
  updated_at: string
}

export interface ReadingStreak {
  id: string
  user_id: string
  current_streak: number
  longest_streak: number
  last_read_date: string | null
  this_week_count: number
  week_start_date: string
  total_reading_days: number
  created_at: string
  updated_at: string
}

export interface UserPreferences {
  id: string
  user_id: string
  preferred_categories: string[]
  reading_goal_minutes_per_day: number
  notification_settings: {
    daily_reminder: boolean
    streak_milestone: boolean
    new_recommendations: boolean
    promotional_emails: boolean
  }
  privacy_settings: {
    share_reading_progress: boolean
    allow_analytics: boolean
    public_profile: boolean
  }
  theme_preference: 'light' | 'dark' | 'auto'
  language_preference: string
  created_at: string
  updated_at: string
}

export interface BookAnalytics {
  id: string
  book_id: number
  date: string
  views_count: number
  reading_sessions: number
  completion_count: number
  favorite_count: number
  total_reading_time_minutes: number
  unique_readers: number
}

export interface UserRecommendation {
  id: string
  user_id: string
  book_id: number
  recommendation_score: number
  recommendation_reason: string
  based_on_books: number[]
  book: any
}

export interface UserSubscription {
  id: string
  user_id: string
  subscription_type: 'free' | 'premium'
  status: 'active' | 'cancelled' | 'expired'
  started_at: string
  expires_at: string | null
  remaining_free_access: number
  created_at: string
  updated_at: string
}

export interface DashboardData {
  recently_read: any[]
  favorites: any[]
  completed: any[]
  in_progress: any[]
  trending: any[]
  recommendations: UserRecommendation[]
  subscription: UserSubscription
  preferences: UserPreferences
  stats: {
    total_summaries_read: number
    total_reading_time: number
    favorites_count: number
    in_progress_count: number
    current_streak: number
    longest_streak: number
    total_reading_days: number
  }
  streak: ReadingStreak
}

class DataService {
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error) {
      console.error('Error getting current user:', error)
      return null
    }
    return user
  }

  async ensureUserSubscription(userId: string): Promise<UserSubscription> {
    // Check if user has a subscription
    const { data: existingSubscription, error } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (existingSubscription && !error) {
      return existingSubscription
    }

    // Create default subscription if it doesn't exist
    const defaultSubscription = {
      user_id: userId,
      subscription_type: 'free' as const,
      status: 'active' as const,
      remaining_free_access: 5
    }

    const { data: newSubscription, error: insertError } = await supabase
      .from('user_subscriptions')
      .insert(defaultSubscription)
      .select()
      .single()

    if (insertError) {
      console.error('Error creating subscription:', insertError)
      // Return default subscription object even if insert fails
      return {
        id: 'temp',
        ...defaultSubscription,
        started_at: new Date().toISOString(),
        expires_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    return newSubscription
  }

  async getDashboardData(): Promise<DashboardData> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }



    // Ensure user has a subscription, streak, and preferences
    const subscription = await this.ensureUserSubscription(user.id)
    const streak = await this.getUserStreak(user.id)
    const preferences = await this.getUserPreferences(user.id)

    // Get user progress with book details - simplified query
    const { data: progress, error } = await supabase
      .from('user_reading_progress')
      .select('*')
      .eq('user_id', user.id)
      .order('last_read_at', { ascending: false })

    if (error) {
      console.error('Error fetching progress:', error)
    }

    // Get books data for progress items
    const progressWithBooks = []
    if (progress && progress.length > 0) {
      const bookIds = progress.map(p => parseInt(p.book_id)).filter(id => !isNaN(id))

      if (bookIds.length > 0) {
        const { data: books } = await supabase
          .from('books')
          .select('*')
          .in('id', bookIds)

        // Combine progress with book data
        for (const prog of progress) {
          const book = books?.find(b => b.id.toString() === prog.book_id)
          if (book) {
            progressWithBooks.push({
              ...prog,
              book: book
            })
          }
        }
      }
    }





    // Temporarily disable trending and recommendations to fix 400 errors
    const trendingBooks = []
    const recommendations = []

    // Separate into different categories
    const recentlyRead = progressWithBooks.filter((p: any) => p.last_read_at).slice(0, 5)
    const favorites = progressWithBooks.filter((p: any) => p.is_favorited)
    const completed = progressWithBooks.filter((p: any) => p.is_completed)
    const inProgress = progressWithBooks.filter((p: any) => p.progress_percentage > 0 && !p.is_completed)

    // Calculate enhanced stats
    const stats = {
      total_summaries_read: completed.length,
      total_reading_time: completed.reduce((acc: number, p: any) =>
        acc + (p.book?.duration || 20), 0),
      favorites_count: favorites.length,
      in_progress_count: inProgress.length,
      current_streak: streak.current_streak,
      longest_streak: streak.longest_streak,
      total_reading_days: streak.total_reading_days
    }

    const dashboardData = {
      recently_read: recentlyRead,
      favorites,
      completed,
      in_progress: inProgress,
      trending: trendingBooks,
      recommendations,
      subscription,
      preferences,
      stats,
      streak
    }


    return dashboardData
  }

  async getUserStreak(userId: string): Promise<ReadingStreak> {
    const { data, error } = await supabase
      .from('user_reading_streak')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error || !data) {
      // Create default streak if doesn't exist
      const defaultStreak = {
        user_id: userId,
        current_streak: 0,
        longest_streak: 0,
        last_read_date: null,
        this_week_count: 0,
        week_start_date: new Date().toISOString().split('T')[0],
        total_reading_days: 0
      }

      const { data: newStreak } = await supabase
        .from('user_reading_streak')
        .insert(defaultStreak)
        .select()
        .single()

      return newStreak || {
        id: 'temp',
        ...defaultStreak,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    return data
  }

  async getUserPreferences(userId: string): Promise<UserPreferences> {
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error || !data) {
      // Create default preferences if doesn't exist
      const defaultPreferences = {
        user_id: userId,
        preferred_categories: [],
        reading_goal_minutes_per_day: 30,
        notification_settings: {
          daily_reminder: true,
          streak_milestone: true,
          new_recommendations: true,
          promotional_emails: false
        },
        privacy_settings: {
          share_reading_progress: true,
          allow_analytics: true,
          public_profile: false
        },
        theme_preference: 'light' as const,
        language_preference: 'pt-BR'
      }

      const { data: newPreferences } = await supabase
        .from('user_preferences')
        .insert(defaultPreferences)
        .select()
        .single()

      return newPreferences || {
        id: 'temp',
        ...defaultPreferences,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    return data
  }

  async getTrendingBooks(): Promise<any[]> {
    // Get trending books based on recent analytics
    const { data, error } = await supabase
      .from('book_analytics')
      .select(`
        book_id,
        views_count,
        reading_sessions,
        completion_count,
        favorite_count,
        book:books(
          id,
          title,
          author,
          category,
          duration,
          cover_image_url,
          is_featured
        )
      `)
      .gte('date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]) // Last 7 days
      .order('reading_sessions', { ascending: false })
      .limit(10)

    if (error) {
      console.error('Error fetching trending books:', error)
      // Fallback to featured books
      const { data: fallbackBooks } = await supabase
        .from('books')
        .select('*')
        .eq('is_featured', true)
        .limit(10)

      return fallbackBooks || []
    }

    return data?.map(item => item.book).filter(Boolean) || []
  }

  async getUserRecommendations(userId: string): Promise<UserRecommendation[]> {
    const { data, error } = await supabase
      .from('user_recommendations')
      .select(`
        *,
        book:books(
          id,
          title,
          author,
          category,
          duration,
          cover_image_url,
          description
        )
      `)
      .eq('user_id', userId)
      .order('recommendation_score', { ascending: false })
      .limit(10)

    if (error) {
      console.error('Error fetching recommendations:', error)
      return []
    }

    return data || []
  }

  async getBookProgress(bookId: string): Promise<UserProgress | null> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('user_reading_progress')
      .select('*')
      .eq('user_id', user.id)
      .eq('book_id', bookId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching book progress:', error)
      return null
    }

    return data
  }

  // Alias for backward compatibility
  async getSummaryProgress(summaryId: string): Promise<UserProgress | null> {
    return this.getBookProgress(summaryId)
  }

  async updateProgress(bookId: string, progressPercentage: number, currentPage: number = 1, totalPages: number = 1, readingTimeMinutes: number = 0): Promise<UserProgress> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    const progressData = {
      user_id: user.id,
      book_id: bookId,
      progress_percentage: Math.min(100, Math.max(0, progressPercentage)),
      current_page: currentPage,
      total_pages: totalPages,
      is_completed: progressPercentage >= 100,
      last_read_at: new Date().toISOString(),
      reading_time_minutes: readingTimeMinutes
    }

    const { data, error } = await supabase
      .from('user_reading_progress')
      .upsert(progressData, {
        onConflict: 'user_id,book_id'
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating progress:', error)
      throw new Error(`Failed to update progress: ${error.message}`)
    }

    // Update reading streak
    await this.updateReadingStreak(user.id)


    return data
  }

  async updateReadingStreak(userId: string): Promise<void> {
    try {
      await supabase.rpc('update_reading_streak', { user_uuid: userId })
    } catch (error) {
      console.error('Error updating reading streak:', error)
    }
  }

  async updateBookAnalytics(bookId: number, eventType: string): Promise<void> {
    try {
      await supabase.rpc('update_book_analytics', {
        book_id_param: bookId,
        event_type: eventType
      })
    } catch (error) {
      console.error('Error updating book analytics:', error)
    }
  }

  async toggleFavorite(bookId: string, isFavorited: boolean): Promise<UserProgress> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    const favoriteData = {
      user_id: user.id,
      book_id: bookId,
      is_favorited: isFavorited,
      last_read_at: new Date().toISOString()
    }

    // Retry logic for database operations
    let retries = 3
    let lastError: Error | null = null

    while (retries > 0) {
      try {
        const { data, error } = await supabase
          .from('user_reading_progress')
          .upsert(favoriteData, {
            onConflict: 'user_id,book_id'
          })
          .select()
          .single()

        if (error) {
          throw new Error(`Database error: ${error.message}`)
        }

        // Update book analytics for favorite action
        if (isFavorited) {
          await this.updateBookAnalytics(parseInt(bookId), 'favorite')
        }

        return data
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        retries--

        if (retries > 0) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, (4 - retries) * 500))
        }
      }
    }

    throw new Error(`Failed to toggle favorite after 3 attempts: ${lastError?.message}`)
  }

  async saveCurrentPage(bookId: string, currentPage: number, totalPages: number): Promise<void> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    const progressPercentage = Math.round((currentPage / totalPages) * 100)

    await this.updateProgress(bookId, progressPercentage, currentPage, totalPages)
  }

  async getCurrentPage(bookId: string): Promise<{ currentPage: number; totalPages: number } | null> {
    const progress = await this.getBookProgress(bookId)
    if (!progress) {
      return null
    }

    return {
      currentPage: progress.current_page,
      totalPages: progress.total_pages
    }
  }

  // Initialize database tables if they don't exist (for development)
  async initializeDatabase() {
    try {
      // This would typically be handled by migrations
      // but we can check if tables exist and create them if needed

    } catch (error) {
      console.error('Error initializing database:', error)
    }
  }

  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: user.id,
        ...preferences,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      })
      .select()
      .single()

    if (error) {
      console.error('Error updating preferences:', error)
      throw error
    }

    return data
  }

  async exportUserData(): Promise<any> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Get all user data
    const [progress, streak, preferences, recommendations] = await Promise.all([
      supabase.from('user_reading_progress').select('*').eq('user_id', user.id),
      supabase.from('user_reading_streak').select('*').eq('user_id', user.id).single(),
      supabase.from('user_preferences').select('*').eq('user_id', user.id).single(),
      supabase.from('user_recommendations').select('*').eq('user_id', user.id)
    ])

    return {
      user_profile: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      },
      reading_progress: progress.data || [],
      reading_streak: streak.data || null,
      preferences: preferences.data || null,
      recommendations: recommendations.data || [],
      exported_at: new Date().toISOString()
    }
  }

  async deleteUserAccount(): Promise<void> {
    const user = await this.getCurrentUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    // Delete all user data (cascade will handle related records)
    await Promise.all([
      supabase.from('user_reading_progress').delete().eq('user_id', user.id),
      supabase.from('user_reading_streak').delete().eq('user_id', user.id),
      supabase.from('user_preferences').delete().eq('user_id', user.id),
      supabase.from('user_recommendations').delete().eq('user_id', user.id),
      supabase.from('user_subscriptions').delete().eq('user_id', user.id)
    ])

    // Finally delete the auth user
    const { error } = await supabase.auth.admin.deleteUser(user.id)
    if (error) {
      console.error('Error deleting user account:', error)
      throw error
    }
  }

  async generateRecommendations(userId: string): Promise<void> {
    // This would typically be run as a background job
    // For now, we'll implement a simple recommendation algorithm

    // Get user's reading history
    const { data: userProgress } = await supabase
      .from('user_reading_progress')
      .select('book_id, is_completed, progress_percentage')
      .eq('user_id', userId)
      .gte('progress_percentage', 50) // Books they've read at least 50%

    if (!userProgress || userProgress.length === 0) {
      return
    }

    // Get categories of books they've read
    const bookIds = userProgress.map(p => p.book_id)
    const { data: readBooks } = await supabase
      .from('books')
      .select('id, category')
      .in('id', bookIds)

    const preferredCategories = [...new Set(readBooks?.map(b => b.category) || [])]

    // Find similar books they haven't read
    const { data: similarBooks } = await supabase
      .from('books')
      .select('id, title, category')
      .in('category', preferredCategories)
      .not('id', 'in', `(${bookIds.join(',')})`)
      .limit(10)

    // Create recommendations
    const recommendations = similarBooks?.map(book => ({
      user_id: userId,
      book_id: book.id,
      recommendation_score: Math.random() * 0.5 + 0.5, // Random score between 0.5-1.0
      recommendation_reason: `Based on your interest in ${book.category}`,
      based_on_books: bookIds.filter((_, i) => i < 3) // Use first 3 books as basis
    })) || []

    if (recommendations.length > 0) {
      await supabase
        .from('user_recommendations')
        .upsert(recommendations, { onConflict: 'user_id,book_id' })
    }
  }
}

export const dataService = new DataService()
